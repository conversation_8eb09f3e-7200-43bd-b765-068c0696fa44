'use client';

import { useState, use } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ImagePreview } from '@/components/ui/image-preview';
import { useOrder, useUpdateOrderTracking } from '@/lib/hooks';
import { OrderStatus, OrderLineItem } from '@/types/order';
import {
  ArrowLeft,
  Package,
  Truck,
  User,
  MapPin,
  CreditCard,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';

interface OrderDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function OrderDetailPage({ params }: OrderDetailPageProps) {
  const router = useRouter();
  const { id } = use(params);
  const orderId = parseInt(id);
  const [isTrackingDialogOpen, setIsTrackingDialogOpen] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [shippingProvider, setShippingProvider] = useState('');
  const [copiedField, setCopiedField] = useState<string | null>(null);

  // Fetch order details
  const { data: order, isLoading, error } = useOrder(orderId);

  // Update tracking mutation
  const { mutate: updateTracking, isPending: isUpdatingTracking } = useUpdateOrderTracking();

  const handleUpdateTracking = () => {
    if (!order) return;

    updateTracking({
      id: order.id,
      data: {
        trackingNumber: trackingNumber || undefined,
        shippingProvider: shippingProvider || undefined,
      }
    }, {
      onSuccess: () => {
        setIsTrackingDialogOpen(false);
        setTrackingNumber('');
        setShippingProvider('');
      }
    });
  };

  const handleCopy = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const getStatusBadgeColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.UNPAID:
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case OrderStatus.AWAITING_SHIPMENT:
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case OrderStatus.AWAITING_COLLECTION:
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case OrderStatus.IN_TRANSIT:
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case OrderStatus.DELIVERED:
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case OrderStatus.COMPLETED:
        return 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200';
      case OrderStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const formatCurrency = (amount?: string, currency?: string) => {
    if (!amount) return 'N/A';
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return amount;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(numAmount);
  };

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" disabled>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="h-8 w-48 bg-muted animate-pulse rounded" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-6 w-32 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 w-full bg-muted animate-pulse rounded" />
                  <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Order Not Found</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-muted-foreground">
              The order you're looking for could not be found. It may have been deleted or you may not have permission to view it.
            </p>
            <Button className="mt-4" onClick={() => router.push('/client/tiktok/order')}>
              Back to Orders
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Order Details</h1>
            <p className="text-muted-foreground">Order ID: {order.idTT || 'N/A'}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusBadgeColor(order.status)}>
            {order.status.replace('_', ' ')}
          </Badge>
          {/* to-do: comming soon */}
          {/* <Button
            variant="outline"
            onClick={() => {
              setTrackingNumber(order.trackingNumber || '');
              setShippingProvider(order.shippingProvider || '');
              setIsTrackingDialogOpen(true);
            }}
          >
            <Edit className="mr-2 h-4 w-4" />
            Update Tracking
          </Button> */}
          {order.idTT && (
            <Button
              variant="outline"
              onClick={() => window.open(`https://seller.tiktokshop.com/orders/${order.idTT}`, '_blank')}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View in TikTok Shop
            </Button>
          )}
        </div>
      </div>

      {/* Order Information Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Order Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Order ID:</span>
              <div className="flex items-center gap-2 min-w-0 flex-1 justify-end">
                <span
                  className="font-medium truncate max-w-[250px]"
                  title={order.idTT || 'N/A'}
                >
                  {order.idTT || 'N/A'}
                </span>
                {order.idTT && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 flex-shrink-0"
                    onClick={() => handleCopy(order.idTT!, 'orderId')}
                  >
                    {copiedField === 'orderId' ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Shop:</span>
              <span
                className="font-medium truncate max-w-[200px] text-right"
                title={order.tiktokShop?.friendly_name || order.tiktokShop?.name || 'N/A'}
              >
                {order.tiktokShop?.friendly_name || order.tiktokShop?.name || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Status:</span>
              <Badge className={getStatusBadgeColor(order.status)}>
                {order.status?.replace('_', ' ') || 'N/A'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Created:</span>
              <span className="font-medium">{formatTimestamp(order.createTimeTT)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Updated:</span>
              <span className="font-medium">{formatTimestamp(order.updateTimeTT)}</span>
            </div>
            {order.paidTime && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Paid:</span>
                <span className="font-medium">{formatTimestamp(order.paidTime)}</span>
              </div>
            )}

            {/* Buyer Message (truncated with title) */}
            {order.buyerMessage && (
              <div className="flex justify-between items-start">
                <span className="text-muted-foreground flex-shrink-0 mr-4">Buyer Message:</span>
                <span
                  className="text-sm bg-muted p-2 rounded max-w-[250px] truncate"
                  title={order.buyerMessage}
                >
                  {order.buyerMessage}
                </span>
              </div>
            )}

            {/* Sample Order */}
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Sample Order:</span>
              <Badge variant={order.isSampleOrder ? 'default' : 'secondary'}>
                {order.isSampleOrder ? 'Yes' : 'No'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Shipping Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Fulfillment Type:</span>
              <span className="font-medium">{order.fulfillmentType || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Shipping Provider:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.shippingProvider || 'N/A'}
              >
                {order.shippingProvider || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Tracking Number:</span>
              <div className="flex items-center gap-2 min-w-0 flex-1 justify-end">
                <span
                  className="font-medium truncate max-w-[150px]"
                  title={order.trackingNumber || 'N/A'}
                >
                  {order.trackingNumber || 'N/A'}
                </span>
                {order.trackingNumber && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 flex-shrink-0"
                    onClick={() => handleCopy(order.trackingNumber!, 'tracking')}
                  >
                    {copiedField === 'tracking' ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Delivery Option:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.deliveryOptionName || 'N/A'}
              >
                {order.deliveryOptionName || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Delivery Type:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.deliveryType || 'N/A'}
              >
                {order.deliveryType || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Shipping Type:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.shippingType || 'N/A'}
              >
                {order.shippingType || 'N/A'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Items */}
      {order.lineItems && order.lineItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Order Items ({order.lineItems.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px] max-w-[300px]">Product</TableHead>
                    <TableHead className="min-w-[120px] max-w-[150px]">SKU</TableHead>
                    <TableHead className="min-w-[80px] w-[100px]">Price</TableHead>
                    <TableHead className="min-w-[80px] w-[100px]">Quantity</TableHead>
                    <TableHead className="min-w-[80px] w-[100px]">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {order.lineItems.map((item: OrderLineItem) => (
                    <TableRow key={item.id}>
                      <TableCell className="min-w-[200px] max-w-[300px]">
                        <div className="flex items-center gap-3">
                          {item.skuImage && (
                            <ImagePreview
                              src={item.skuImage}
                              alt={item.productName || 'Product'}
                              productName={item.productName || 'Product'}
                              className="flex-shrink-0"
                            />
                          )}
                          <div className="min-w-0 flex-1">
                            <p
                              className="font-medium truncate"
                              title={item.productName || 'N/A'}
                            >
                              {item.productName || 'N/A'}
                            </p>
                            <p
                              className="text-sm text-muted-foreground truncate"
                              title={item.skuName || 'N/A'}
                            >
                              {item.skuName || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm min-w-[120px] max-w-[150px]">
                        <span
                          className="truncate block"
                          title={item.sellerSku || item.skuIdTT || 'N/A'}
                        >
                          {item.sellerSku || item.skuIdTT || 'N/A'}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.salePrice?.toString(), item.currency)}
                      </TableCell>
                      <TableCell className="text-right">
                        {item.quantity || 1}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(((item.salePrice || 0) * (item.quantity || 1)).toString(), item.currency)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* TO-DO: sensitive information *
      * Customer and Payment Information Grid *
      <div className="grid gap-6 md:grid-cols-2">
        {/* Customer Information *
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Email:</span>
              <div className="flex items-center gap-2 min-w-0 flex-1 justify-end">
                <span
                  className="font-medium truncate max-w-[200px]"
                  title={order.buyerEmail || 'N/A'}
                >
                  {order.buyerEmail || 'N/A'}
                </span>
                {order.buyerEmail && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 flex-shrink-0"
                    onClick={() => handleCopy(order.buyerEmail!, 'email')}
                  >
                    {copiedField === 'email' ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">TikTok User ID:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.userIdTT || 'N/A'}
              >
                {order.userIdTT || 'N/A'}
              </span>
            </div>
            {order.buyerMessage && (
              <div>
                <span className="text-muted-foreground">Message:</span>
                <p className="mt-1 text-sm bg-muted p-2 rounded">{order.buyerMessage}</p>
              </div>
            )}

            {/* Shipping Address Section *
            {order.recipientAddress && (
              <>
                <div className="border-t pt-4 mt-4">
                  <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Shipping Address
                  </h4>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground flex-shrink-0">Recipient Name:</span>
                  <span
                    className="font-medium truncate max-w-[200px] text-right"
                    title={order.recipientAddress.name || 'N/A'}
                  >
                    {order.recipientAddress.name || 'N/A'}
                  </span>
                </div>
                {order.recipientAddress.phone && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground flex-shrink-0">Phone:</span>
                    <span
                      className="font-medium truncate max-w-[200px] text-right"
                      title={order.recipientAddress.phone}
                    >
                      {order.recipientAddress.phone}
                    </span>
                  </div>
                )}
                {order.recipientAddress.addressLine1 && (
                  <div className="flex justify-between items-start">
                    <span className="text-muted-foreground flex-shrink-0 mr-4">Address:</span>
                    <div className="text-right max-w-[200px]">
                      <div className="text-sm">
                        <p className="font-medium">{order.recipientAddress.addressLine1}</p>
                        {order.recipientAddress.addressLine2 && (
                          <p className="font-medium">{order.recipientAddress.addressLine2}</p>
                        )}
                        <p className="text-muted-foreground">
                          {[
                            order.recipientAddress.city,
                            order.recipientAddress.state,
                            order.recipientAddress.postalCode
                          ].filter(Boolean).join(', ')}
                        </p>
                        {order.recipientAddress.country && (
                          <p className="text-muted-foreground">{order.recipientAddress.country}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Payment Information *
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Amount:</span>
              <span className="font-bold text-lg">
                {formatCurrency(order.payment?.totalAmount, order.payment?.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal:</span>
              <span className="font-medium">
                {formatCurrency(order.payment?.subTotal, order.payment?.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Shipping Fee:</span>
              <span className="font-medium">
                {formatCurrency(order.payment?.shippingFee, order.payment?.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Tax:</span>
              <span className="font-medium">
                {formatCurrency(order.payment?.tax, order.payment?.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Platform Discount:</span>
              <span className="font-medium">
                {formatCurrency(order.payment?.platformDiscount, order.payment?.currency)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex-shrink-0">Payment Method:</span>
              <span
                className="font-medium truncate max-w-[150px] text-right"
                title={order.paymentMethodName || 'N/A'}
              >
                {order.paymentMethodName || 'N/A'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
        */}


      {/* Update Tracking Dialog */}
      <Dialog open={isTrackingDialogOpen} onOpenChange={setIsTrackingDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Tracking Information</DialogTitle>
            <DialogDescription>
              Update the tracking number and shipping provider for this order.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tracking-number">Tracking Number</Label>
              <Input
                id="tracking-number"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                placeholder="Enter tracking number"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shipping-provider">Shipping Provider</Label>
              <Input
                id="shipping-provider"
                value={shippingProvider}
                onChange={(e) => setShippingProvider(e.target.value)}
                placeholder="Enter shipping provider"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTrackingDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTracking} disabled={isUpdatingTracking}>
              {isUpdatingTracking ? 'Updating...' : 'Update Tracking'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
